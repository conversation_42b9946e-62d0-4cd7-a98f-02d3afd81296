#!/usr/bin/env python3
"""
Test script to verify the config_manager integration with context management.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_manager_integration():
    """Test that config_manager.get() method works correctly."""
    print("🧪 Testing ConfigManager Integration")
    print("=" * 50)
    
    try:
        # Import the config manager
        from config_manager import config_manager
        
        print("✅ ConfigManager imported successfully")
        
        # Test the get method with our new config options
        context_management = config_manager.get('AGENT_CONTEXT_MANAGEMENT', False)
        context_strategy = config_manager.get('AGENT_CONTEXT_STRATEGY', 'default')
        
        print(f"✅ AGENT_CONTEXT_MANAGEMENT: {context_management}")
        print(f"✅ AGENT_CONTEXT_STRATEGY: {context_strategy}")
        
        # Test that the method exists and works
        test_value = config_manager.get('NONEXISTENT_KEY', 'default_value')
        print(f"✅ Default value test: {test_value}")
        
        # Test with a known config value
        max_context = config_manager.get('MAX_RESPONSE_CONTEXT', 16384)
        print(f"✅ MAX_RESPONSE_CONTEXT: {max_context}")
        
        print("\n🎉 All ConfigManager integration tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_worker_context_management():
    """Test that Worker can access config_manager.get() method."""
    print("\n🔧 Testing Worker Context Management Integration")
    print("=" * 50)
    
    try:
        # Import required modules
        from config_manager import config_manager
        
        # Create a minimal mock worker to test the method call
        class MockWorker:
            def __init__(self):
                self.config_manager = config_manager
            
            def test_context_management_config(self):
                # This is the exact line that was failing
                context_management_enabled = self.config_manager.get('AGENT_CONTEXT_MANAGEMENT', True)
                return context_management_enabled
        
        # Test the worker
        worker = MockWorker()
        result = worker.test_context_management_config()
        
        print(f"✅ Worker config access successful: {result}")
        print("✅ No 'get_config_value' error occurred")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 ConfigManager Integration Test Suite")
    print("=" * 60)
    
    # Test 1: Basic config manager functionality
    test1_success = test_config_manager_integration()
    
    # Test 2: Worker integration
    test2_success = test_worker_context_management()
    
    print("\n" + "=" * 60)
    if test1_success and test2_success:
        print("✅ ALL TESTS PASSED - ConfigManager integration is working!")
        print("✅ The 'get_config_value' error has been fixed")
        print("✅ Context management will work correctly in multi-agent workflows")
    else:
        print("❌ SOME TESTS FAILED - Please check the implementation")
    
    print("\n📋 Fix Summary:")
    print("   - Changed config_manager.get_config_value() to config_manager.get()")
    print("   - Context management now uses correct ConfigManager API")
    print("   - Multi-agent workflows will process without config errors")
