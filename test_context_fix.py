#!/usr/bin/env python3
"""
Simple test to verify the context management fix works.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_config_manager_fix():
    """Test that the ConfigManager fix works correctly."""
    print("🧪 Testing ConfigManager Fix")
    print("=" * 40)
    
    try:
        # Import the ConfigManager class
        from config_manager import ConfigManager
        
        # Create an instance
        config_manager = ConfigManager()
        
        print("✅ ConfigManager imported and instantiated successfully")
        
        # Test the get method that we're using in worker.py
        context_management = config_manager.get('AGENT_CONTEXT_MANAGEMENT', True)
        context_strategy = config_manager.get('AGENT_CONTEXT_STRATEGY', 'intelligent_truncation')
        
        print(f"✅ AGENT_CONTEXT_MANAGEMENT: {context_management}")
        print(f"✅ AGENT_CONTEXT_STRATEGY: {context_strategy}")
        
        # Test that the method signature matches what we're using
        test_default = config_manager.get('NONEXISTENT_KEY', 'default_value')
        print(f"✅ Default value handling: {test_default}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_worker_context_method():
    """Test that the Worker context management method would work."""
    print("\n🔧 Testing Worker Context Management Method")
    print("=" * 50)
    
    try:
        from config_manager import ConfigManager
        
        # Simulate the Worker class context management method
        class MockWorker:
            def __init__(self):
                self.config_manager = ConfigManager()
            
            def _format_agent_responses_with_context_management(self, agent_responses, current_agent_number, provider, model):
                """Simplified version of the method that was failing."""
                if not agent_responses:
                    return ""
                
                # This is the line that was causing the error
                context_management_enabled = self.config_manager.get('AGENT_CONTEXT_MANAGEMENT', True)
                
                if not context_management_enabled:
                    return "Context management disabled"
                else:
                    return "Context management enabled"
        
        # Test the method
        worker = MockWorker()
        result = worker._format_agent_responses_with_context_management({1: "test"}, 2, "OpenRouter", "test-model")
        
        print(f"✅ Worker method executed successfully: {result}")
        print("✅ No 'get_config_value' AttributeError occurred")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Context Management Fix Verification")
    print("=" * 50)
    
    # Test 1: Basic ConfigManager functionality
    test1_success = test_config_manager_fix()
    
    # Test 2: Worker method simulation
    test2_success = test_worker_context_method()
    
    print("\n" + "=" * 50)
    if test1_success and test2_success:
        print("✅ ALL TESTS PASSED!")
        print("✅ The ConfigManager fix is working correctly")
        print("✅ Multi-agent workflows should now work without errors")
        print("\n📋 What was fixed:")
        print("   - Changed config_manager.get_config_value() to config_manager.get()")
        print("   - This matches the actual ConfigManager API")
        print("   - Context management will now work in agent processing")
    else:
        print("❌ SOME TESTS FAILED")
        print("❌ Please check the ConfigManager implementation")
